import { Editor, Element, Path, Range, Text } from 'slate';
import File from '../../../entities/file';

/**
 * 计算光标选中位置对应的原始文件实际位置
 * @param file Slate编辑器实例
 * @param editor Slate编辑器实例
 * @param selection 选区对象
 * @returns 文件中的位置信息，如果无法计算则返回null
 */
export function calculateFilePosition(file: File, editor: Editor, selection: Range): FileRange | null {
    try {
        // 只在有选区（非折叠状态）时计算
        if (Range.isCollapsed(selection)) {
            return null;
        }

        const [start, end] = Range.edges(selection);

        // 获取选区开始位置的信息
        const startPosition = getPositionFromPoint(editor, start.path, start.offset);
        if (!startPosition) return null;

        // 获取选区结束位置的信息
        const endPosition = getPositionFromPoint(editor, end.path, end.offset);
        if (!endPosition) return null;

        // 获取选中区域的实际文本内容
        const selectedText = getTextFromFileRange(file, startPosition, endPosition);

        return {
            start: startPosition,
            end: endPosition,
            text: selectedText
        };
    } catch (error) {
        console.error('计算文件位置时出错:', error);
        return null;
    }
}

/**
 * 从原始文件内容中获取指定范围的文本
 * @param file 文件实例
 * @param start 开始位置
 * @param end 结束位置
 * @returns 选中区域的文本内容
 */
function getTextFromFileRange(file: File, start: FilePosition, end: FilePosition): string {
    try {
        // 将 Buffer 转换为字符串
        const content = file.content.toString('utf-8');
        const lines = content.split('\n');

        // 如果开始和结束在同一行
        if (start.line === end.line) {
            const line = lines[start.line - 1]; // 行号从1开始，数组从0开始
            if (line) {
                return line.substring(start.column - 1, end.column - 1); // 列号从1开始
            }
            return '';
        }

        // 跨多行的情况
        let result = '';

        // 第一行：从开始列到行尾
        const firstLine = lines[start.line - 1];
        if (firstLine !== undefined) {
            result += firstLine.substring(start.column - 1);
            // 只有当不是最后一行时才添加换行符
            if (start.line < end.line) {
                result += '\n';
            }
        }

        // 中间的完整行
        for (let i = start.line; i < end.line - 1; i++) {
            if (lines[i] !== undefined) {
                result += lines[i] + '\n';
            }
        }

        // 最后一行：从行首到结束列
        if (end.line > start.line) {
            const lastLine = lines[end.line - 1];
            if (lastLine !== undefined) {
                result += lastLine.substring(0, end.column - 1);
            }
        }

        return result;
    } catch (error) {
        console.error('获取文件范围文本时出错:', error);
        return '';
    }
}

/**
 * 根据路径和偏移量获取文件位置
 * @param editor Slate编辑器实例
 * @param path 节点路径
 * @param offset 偏移量
 * @returns 文件位置信息
 */
function getPositionFromPoint(editor: Editor, path: Path, offset: number): FilePosition | null {
    try {
        const [node] = Editor.node(editor, path);
        console.log(node);
        // 如果是文本节点且有位置信息
        if (Text.isText(node) && node.position) {
            const { position } = node;
            const text = node.text;

            // 如果偏移量为0，返回开始位置
            if (offset === 0) {
                return {
                    line: position.start.line,
                    column: position.start.column
                };
            }

            // 如果偏移量超出文本长度，返回结束位置
            if (offset >= text.length) {
                return {
                    line: position.end.line,
                    column: position.end.column
                };
            }

            // 计算偏移量对应的实际位置
            const beforeOffset = text.substring(0, offset);
            const lines = beforeOffset.split('\n');
            const lineOffset = lines.length - 1;
            const columnOffset = lines[lines.length - 1].length;

            return {
                line: position.start.line + lineOffset,
                column: lineOffset === 0 ? position.start.column + columnOffset : columnOffset + 1
            };
        }

        // 处理没有位置信息的文本节点
        if (Text.isText(node) && !node.position) {
            // 尝试从父节点获取位置信息
            if (path.length > 0) {
                const parentPath = Path.parent(path);
                const [parentNode] = Editor.node(editor, parentPath);

                // 如果父节点是 codeLine，继续向上查找 code 节点
                if (Element.isElement(parentNode) && parentNode.type === 'codeLine') {
                    const codeLinePath = parentPath;
                    const codeLineIndex = codeLinePath[codeLinePath.length - 1];

                    if (codeLinePath.length > 0) {
                        const codePath = Path.parent(codeLinePath);
                        const [codeNode] = Editor.node(editor, codePath);

                        // 如果是代码块节点且有位置信息
                        if (Element.isElement(codeNode) && codeNode.type === 'code' && codeNode.position) {
                            const { position } = codeNode;
                            // 代码块的实际内容从 position.start.line + 1 开始（跳过开头的 ```）
                            // 计算在代码块中的实际行号
                            const actualLine = position.start.line + 1 + codeLineIndex;

                            // 确保不超过代码块的结束行（减去结尾的 ```）
                            const maxLine = position.end.line - 1;
                            const finalLine = Math.min(actualLine, maxLine);

                            return {
                                line: finalLine,
                                column: 1 + offset // 代码行从第1列开始，加上偏移量
                            };
                        }
                    }
                }

                // 如果父节点是 paragraph 或其他有位置信息的元素
                if (Element.isElement(parentNode) && parentNode.position) {
                    const { position } = parentNode;

                    // 计算在父元素中的偏移量
                    // 需要考虑同级的其他文本节点
                    let textOffset = 0;
                    const textIndex = path[path.length - 1];

                    // 累加前面兄弟文本节点的长度
                    for (let i = 0; i < textIndex; i++) {
                        const siblingPath = [...parentPath, i];
                        try {
                            const [siblingNode] = Editor.node(editor, siblingPath);
                            if (Text.isText(siblingNode)) {
                                textOffset += siblingNode.text.length;
                            }
                        } catch {
                            // 忽略无效路径
                        }
                    }

                    // 加上当前节点内的偏移量
                    const totalOffset = textOffset + offset;

                    // 根据总偏移量计算实际位置
                    const allText = Editor.string(editor, parentPath);
                    const beforeOffset = allText.substring(0, totalOffset);
                    const lines = beforeOffset.split('\n');
                    const lineOffset = lines.length - 1;
                    const columnOffset = lines[lines.length - 1].length;

                    return {
                        line: position.start.line + lineOffset,
                        column: lineOffset === 0 ? position.start.column + columnOffset : columnOffset + 1
                    };
                }
            }
        }

        // 如果是元素节点且有位置信息
        if (Element.isElement(node) && node.position) {
            const { position } = node;

            // 如果偏移量为0，返回开始位置
            if (offset === 0) {
                return {
                    line: position.start.line,
                    column: position.start.column
                };
            }

            // 对于元素节点，如果有偏移量，返回结束位置
            return {
                line: position.end.line,
                column: position.end.column
            };
        }

        // 尝试从父节点获取位置信息
        if (path.length > 0) {
            const parentPath = Path.parent(path);
            const [parentNode] = Editor.node(editor, parentPath);

            if (Element.isElement(parentNode) && parentNode.position) {
                return {
                    line: parentNode.position.start.line,
                    column: parentNode.position.start.column
                };
            }
        }

        return null;
    } catch (error) {
        console.error('获取节点位置时出错:', error);
        return null;
    }
}

/**
 * 根据行列位置查找对应的Slate路径和偏移量
 * @param editor Slate编辑器实例
 * @param line 行号（从1开始）
 * @param column 列号（从1开始）
 * @returns Slate路径和偏移量，如果找不到则返回null
 */
export function findSlatePositionFromFilePosition(
    editor: Editor,
    line: number,
    column: number
): { path: Path; offset: number } | null {
    try {
        // 遍历所有节点查找匹配的位置
        for (const [node, path] of Editor.nodes(editor, { at: [] })) {
            if (Text.isText(node) && node.position) {
                const { position } = node;

                // 检查行号是否在范围内
                if (line >= position.start.line && line <= position.end.line) {
                    // 如果是单行文本
                    if (position.start.line === position.end.line) {
                        if (column >= position.start.column && column <= position.end.column) {
                            const offset = column - position.start.column;
                            return { path, offset };
                        }
                    } else {
                        // 多行文本的处理
                        if (line === position.start.line && column >= position.start.column) {
                            const offset = column - position.start.column;
                            return { path, offset };
                        } else if (line === position.end.line && column <= position.end.column) {
                            // 计算到目标行的偏移量
                            const text = node.text;
                            const lines = text.split('\n');
                            let offset = 0;

                            for (let i = 0; i < line - position.start.line; i++) {
                                offset += lines[i].length + 1; // +1 for newline
                            }
                            offset += column - 1;

                            return { path, offset };
                        } else if (line > position.start.line && line < position.end.line) {
                            // 在中间行
                            const text = node.text;
                            const lines = text.split('\n');
                            let offset = 0;

                            for (let i = 0; i < line - position.start.line; i++) {
                                offset += lines[i].length + 1;
                            }
                            offset += column - 1;

                            return { path, offset };
                        }
                    }
                }
            }
        }

        return null;
    } catch (error) {
        console.error('查找Slate位置时出错:', error);
        return null;
    }
}
