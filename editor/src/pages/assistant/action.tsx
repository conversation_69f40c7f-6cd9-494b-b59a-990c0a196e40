import { GoPlus } from 'react-icons/go';
import { VscChromeClose, VscHistory, VscReply, VscSettingsGear } from 'react-icons/vsc';
import Button from '../../components/button';
import Tooltip from '../../components/tooltip';
import { useContext } from './context';

export default function Action() {
    const { reset, windowState, setWindowState } = useContext();

    if (windowState !== 'chat') {
        return <div className={'d-flex gap-1'}>
            <Tooltip tooltip={'返回'}>
                <Button variant='light' onClick={() => setWindowState('chat')}>
                    <VscReply />
                </Button>
            </Tooltip>
        </div>;
    }

    return <div className={'d-flex'}>
        <Tooltip tooltip={'assistant.new'}>
            <Button variant='light' onClick={() => reset()}><GoPlus /></Button>
        </Tooltip>
        <Tooltip tooltip={'assistant.history'}>
            <Button variant='light' onClick={() => setWindowState('history')}>
                <VscHistory />
            </Button>
        </Tooltip>
        <Tooltip tooltip={'assistant.settings'}>
            <Button className={'d-none'} variant='light' onClick={() => setWindowState('settings')}>
                <VscSettingsGear />
            </Button>
        </Tooltip>
        <Tooltip tooltip={'关闭'}>
            <Button variant='light' onClick={() => {
            }}>
                <VscChromeClose />
            </Button>
        </Tooltip>
    </div>;
}
